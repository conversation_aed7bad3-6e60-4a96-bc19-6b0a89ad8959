import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { RiCloseLine } from "@remixicon/react";

interface Source {
  source: {
    name: string;
    source: string;
    file_id: string;
    created_by: number;
    organization_id: number;
  };
  document: string[];
  distances: number[];
  metadata: Array<{
    name: string;
    source: string;
    file_id: string;
    created_by: number;
    organization_id: number;
  }>;
}

interface MessageItem {
  id: string;
  text: string;
  ai_text: string;
  deid_text: string;
  reid_text: string;
  message_type: string;
  metadata?: {
    sources?: Source[];
  };
  created_at: string;
  prompt: any;
  is_doc_deleted: boolean;
  is_bad_response: boolean;
}

interface CitationInfoProps {
  messageItem: MessageItem;
}

interface CitationModalProps {
  show: boolean;
  onClose: () => void;
  source: Source;
  sourceIndex: number;
}

const CitationModal: React.FC<CitationModalProps> = ({
  show,
  onClose,
  source,
  sourceIndex,
}) => {
  const relevancePercentage = (
    (1 - source.distances[sourceIndex]) *
    100
  ).toFixed(2);
  const fileName = source.metadata[sourceIndex]?.name || source.source.name;

  return (
    <Modal show={show} onHide={onClose} keyboard={false} centered size="lg">
      <Modal.Body
        className="position-relative p-4"
        style={{ borderRadius: "20px" }}
      >
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={onClose}
          style={{
            width: "60px",
            height: "60px",
            padding: "10px",
            top: "-30px",
            right: "-30px",
          }}
        >
          <RiCloseLine size={40} color="#f9f9f9" />
        </Button>

        <div className="w-100">
          <h4 className="mb-4 fw-bold">Citation</h4>

          <div className="mb-4">
            <h6 className="fw-bold mb-2">Source</h6>
            <p className="text-decoration-underline cursor-pointer text-primary mb-0">
              {fileName}
            </p>
          </div>

          <div className="mb-4">
            <h6 className="fw-bold mb-2">Relevance</h6>
            <div className="d-flex align-items-center">
              <span
                className="px-2 py-1 rounded text-white fw-bold"
                style={{
                  backgroundColor: "#ff8c00",
                  fontSize: "14px",
                }}
              >
                {relevancePercentage}%
              </span>
              <span className="text-muted ms-2" style={{ fontSize: "14px" }}>
                ({source.distances[sourceIndex].toFixed(4)})
              </span>
            </div>
          </div>

          <div className="mb-3">
            <h6 className="fw-bold mb-2">Content</h6>
            <div
              className="p-3 rounded border"
              style={{
                backgroundColor: "#f8f9fa",
                maxHeight: "300px",
                overflowY: "auto",
              }}
            >
              <div
                className="mb-0"
                style={{
                  whiteSpace: "pre-wrap",
                  fontFamily: "inherit",
                  lineHeight: "1.5",
                  fontSize: "14px",
                }}
              >
                {source.document[sourceIndex]}
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

const CitationInfo: React.FC<CitationInfoProps> = ({ messageItem }) => {
  const [selectedSource, setSelectedSource] = useState<{
    source: Source;
    index: number;
  } | null>(null);
  const [showModal, setShowModal] = useState(false);

  const sources = messageItem?.metadata?.sources || [];

  const handleSourceClick = (source: Source, index: number) => {
    setSelectedSource({ source, index });
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedSource(null);
  };

  if (!sources.length) {
    return null;
  }

  return (
    <>
      <div className="citation-info mt-2">
        {sources.map((source, sourceIdx) => {
          const citationItems = source.document.map((_, docIndex) => {
            const citationNumber = sourceIdx + 1;
            const fileName =
              source.metadata[docIndex]?.name || source.source.name;

            return (
              <div
                key={`${sourceIdx}-${docIndex}`}
                className="citation-item mb-1 d-flex align-items-center"
              >
                <span
                  className="me-2 fw-bold"
                  style={{ color: "#0d3149", minWidth: "20px" }}
                >
                  {citationNumber}
                </span>
                <span
                  className="text-decoration-underline cursor-pointer"
                  onClick={() => handleSourceClick(source, docIndex)}
                  style={{
                    cursor: "pointer",
                    color: "#0d3149",
                    fontSize: "14px",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.textDecoration = "underline";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.textDecoration = "none";
                  }}
                >
                  {fileName}
                </span>
              </div>
            );
          });

          return citationItems;
        })}
      </div>

      {selectedSource && (
        <CitationModal
          show={showModal}
          onClose={handleCloseModal}
          source={selectedSource.source}
          sourceIndex={selectedSource.index}
        />
      )}
    </>
  );
};

export default CitationInfo;

import React, { useState } from "react";
import "./styles.scss";
import { CitationModal } from "components/Common";

interface Source {
  source: {
    name: string;
    source: string;
    file_id: string;
    created_by: number;
    organization_id: number;
  };
  document: string[];
  distances: number[];
  metadata: Array<{
    name: string;
    source: string;
    file_id: string;
    created_by: number;
    organization_id: number;
  }>;
}

interface MessageItem {
  id: string;
  text: string;
  ai_text: string;
  deid_text: string;
  reid_text: string;
  message_type: string;
  metadata?: {
    sources?: Source[];
  };
  created_at: string;
  prompt: any;
  is_doc_deleted: boolean;
  is_bad_response: boolean;
}

interface CitationInfoProps {
  messageItem: MessageItem;
}

const CitationInfo: React.FC<CitationInfoProps> = ({ messageItem }) => {
  const [selectedSource, setSelectedSource] = useState<{
    source: Source;
    index: number;
  } | null>(null);
  const [showModal, setShowModal] = useState(false);

  const sources = messageItem?.metadata?.sources || [];

  const handleSourceClick = (source: Source, index: number) => {
    setSelectedSource({ source, index });
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedSource(null);
  };

  if (!sources.length) {
    return null;
  }

  return (
    <>
      <div className="citation-info">
        <div className="d-flex flex-wrap gap-2">
          {sources.map((source, sourceIdx) => {
            return (
              <div
                key={`${sourceIdx}`}
                className="citation-tile"
                onClick={() => handleSourceClick(source, sourceIdx)}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#e9ecef";
                  e.currentTarget.style.borderColor = "#0d3149";
                  e.currentTarget.style.transform = "translateY(-1px)";
                  e.currentTarget.style.boxShadow =
                    "0 2px 4px rgba(0, 0, 0, 0.1)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "#f8f9fa";
                  e.currentTarget.style.borderColor = "#dee2e6";
                  e.currentTarget.style.transform = "translateY(0)";
                  e.currentTarget.style.boxShadow = "none";
                }}
                title={source.source.name}
              >
                <span className="citation-number">{sourceIdx + 1}</span>
                <span className="citation-filename text-capitalize">
                  {source.source.name}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {selectedSource && (
        <CitationModal
          show={showModal}
          onClose={handleCloseModal}
          source={selectedSource.source}
        />
      )}
    </>
  );
};

export default CitationInfo;

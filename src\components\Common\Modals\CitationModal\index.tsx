import { RiCloseLine } from "@remixicon/react";
import { But<PERSON>, Modal } from "react-bootstrap";
import "./styles.scss";

interface Source {
  source: {
    name: string;
    source: string;
    file_id: string;
    created_by: number;
    organization_id: number;
  };
  document: string[];
  distances: number[];
  metadata: Array<{
    name: string;
    source: string;
    file_id: string;
    created_by: number;
    organization_id: number;
    page?: number;
  }>;
}

interface CitationModalProps {
  show: boolean;
  onClose: () => void;
  source: Source;
}

const CitationModal: React.FC<CitationModalProps> = ({
  show,
  onClose,
  source,
}) => {
  return (
    <Modal
      show={show}
      onHide={onClose}
      keyboard={false}
      centered
      className="citation-modal"
    >
      <Modal.Body
        className="position-relative p-4"
        style={{ borderRadius: "20px" }}
      >
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={onClose}
        >
          <RiCloseLine size={40} color="#f9f9f9" />
        </Button>

        <div className="w-100 citation-content">
          <h4 className="mb-3 fw-bold">Citation</h4>

          {source?.document?.map((doc, index) => {
            const relevancePercentage = (source.distances[index] * 100).toFixed(
              2
            );
            const fileName =
              source.metadata[index]?.name || source.source.name;
            // const pageNumber = source.metadata[index]?.page;

            return (
              <div
                key={index}
                className="mb-4"
              >
                <div className="mb-2">
                  <h6 className="fw-bold m-0">Source</h6>
                  <p className="text-decoration-underline m-0">
                    {fileName}
                    {/* {pageNumber && ` (Page ${pageNumber})`} */}
                  </p>
                </div>

                <div className="mb-2">
                  <h6 className="fw-bold">Relevance</h6>
                  <div className="d-flex align-items-center">
                    <span className="px-2 rounded text-white fw-bold bg-brown">
                      {relevancePercentage}%
                    </span>
                    <span
                      className="text-muted ms-2"
                      style={{ fontSize: "14px" }}
                    >
                      ({source.distances[index].toFixed(4)})
                    </span>
                  </div>
                </div>

                <div>
                  <h6 className="fw-bold m-0">Content</h6>
                  <div
                    className="mb-0"
                    style={{
                      whiteSpace: "pre-wrap",
                      fontFamily: "inherit",
                      lineHeight: "1.5",
                      fontSize: "14px",
                    }}
                  >
                    {doc}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default CitationModal;

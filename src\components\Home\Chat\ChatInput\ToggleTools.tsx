import { Form } from "react-bootstrap";

interface ToggleToolsProps {
  toolsConfig: Record<string, boolean>;
  setToolsConfig: (value: Record<string, boolean>) => void;
}

const ToggleTools = ({ toolsConfig, setToolsConfig }: ToggleToolsProps) => {

  const handleToggleTools = (evt: any) => {
    setToolsConfig({
      ...toolsConfig,
      [evt.target.name]: evt.target.checked,
    });
  };
  return (
    <Form.Group className={`m-0 p-0 file-input-label tools position-absolute`}>
      <Form.Check
        type="switch"
        onChange={handleToggleTools}
        checked={toolsConfig?.knowledge_base}
        name="knowledge_base"
        // disabled
      />
    </Form.Group>
  );
};

export default ToggleTools;

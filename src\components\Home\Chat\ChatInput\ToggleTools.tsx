import { useFeatureEnable, useFeatureFlags } from "hooks";
import { Form } from "react-bootstrap";

interface ToggleToolsProps {
  toolsConfig: Record<string, boolean>;
  setToolsConfig: (value: Record<string, boolean>) => void;
}

const ToggleTools = ({ toolsConfig, setToolsConfig }: ToggleToolsProps) => {
  const { isFeatureEnabled } = useFeatureFlags();
  const { isKnowledgeBaseEnabled } = useFeatureEnable();

  const handleToggleTools = (evt: any) => {
    setToolsConfig({
      ...toolsConfig,
      [evt.target.name]: evt.target.checked,
    });
  };

  const isKnowledgeBaseAvailable =
    isFeatureEnabled("KNOWLEDGE_BASE") && isKnowledgeBaseEnabled;

  return (
    <Form.Group className={`m-0 p-0 file-input-label tools position-absolute`} title="Toggle Knowledge Base">
      <Form.Check
        type="switch"
        onChange={handleToggleTools}
        checked={toolsConfig?.knowledge_base && isKnowledgeBaseAvailable}
        name="knowledge_base"
        disabled={!isKnowledgeBaseAvailable}
      />
    </Form.Group>
  );
};

export default ToggleTools;

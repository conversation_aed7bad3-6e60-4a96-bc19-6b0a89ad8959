@use "/src/styles/mixins/mixins.scss" as mixins;

$color-canvas-default: #ffffff;
$color-canvas-subtle: #f6f8fa;
$color-border-default: #d0d7de;
$color-border-muted: hsla(210, 18%, 87%, 1);

.incoming {
  max-width: 55%;
  margin-right: auto;

  @media only screen and (max-width: 1599px) {
    max-width: 75%;
  }

  @media only screen and (max-width: 767px) {
    max-width: 90%;
  }

  @media only screen and (max-width: 576px) {
    max-width: 100%;
    font-size: 14px;
  }

  &-data {
    gap: 15px;

    &-user {
      padding-top: 20px;
    }

    &-details {
      line-height: 1;
      gap: 10px;

      &-timing {
        color: rgba(26, 26, 26, 0.5);
        font-size: 14px;
        font-weight: 600;
      }

      .user {
        @include mixins.user-chat-message;

        &-message-text {
          line-height: 25px;
          max-width: min(70vw, 800px);

          @media only screen and (max-width: 1599px) {
            max-width: min(50vw, 800px);
          }

          pre {
            @include mixins.slim-scrollbar;
          }

          & > p:not(:nth-child(1)) {
            margin-top: 15px;
            margin-bottom: 0px;
          }

          p:is(:has(+ ul), :has(+ ol)) {
            margin-bottom: 0;
          }

          p + ul,
          p + ol {
            margin-bottom: 0.5rem;
          }

          @media only screen and (max-width: 576px) {
            line-height: 21px;
            max-width: 250px;
          }
        }
      }
    }

    &-logo {
      width: 55px;
      height: 55px;
      padding: 13px;
      background-color: #0d3149;

      @media only screen and (max-width: 576px) {
        width: 35px;
        height: 35px;
        padding: 8px;
      }
    }
  }

  table {
    border-spacing: 0;
    border-collapse: collapse;
    display: block;
    margin-top: 0;
    margin-bottom: 16px;
    width: max-content;
    max-width: 100%;
    overflow: auto;
    @include mixins.slim-scrollbar(#d0d7de);
  }

  tr {
    background-color: $color-canvas-default;
    border-top: 1px solid $color-border-muted;
  }

  tr:nth-child(2n) {
    background-color: $color-canvas-subtle;
  }

  td,
  th {
    padding: 6px 13px;
    border: 1px solid $color-border-default;
  }

  th {
    font-weight: 600;
  }

  table img {
    background-color: transparent;
  }

  .shimmer {
    font-weight: 500;
    height: 25px;
    color: #0d3149;
    display: inline-block;
    mask: linear-gradient(-60deg, #000 30%, #0005, #000 70%) right/300% 100%;
    -webkit-mask: linear-gradient(-60deg, #000 30%, #0005, #000 70%) right/300%
      100%;
    background-repeat: no-repeat;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    100% {
      mask-position: left;
      -webkit-mask-position: left;
    }
  }

  .citation-info {
    margin-top: 12px;
    padding: 10px 0;
    border-top: 1px solid #e9ecef;

    .citation-item {
      font-size: 14px;
      line-height: 1.5;
      padding: 2px 0;

      &:hover {
        span:last-child {
          color: #0056b3 !important;
        }
      }
    }
  }
}

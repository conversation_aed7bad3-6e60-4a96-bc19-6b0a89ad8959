import { RiArrowUpCircleFill } from "@remixicon/react";
import { useSendMessageMutation } from "api/chat.api";
import {
  useDragAndDrop,
  useFileUpload,
  usePasteFile,
  useTranscription,
} from "hooks";
import { useEffect, useMemo, useRef, useState } from "react";
import { Button, Form } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import Rollbar from "rollbar";
import { ROUTE_PATH } from "routes";
import { setIsDocDeleted, setMessageHistory, setTempNewChat } from "stores";
import useChatStore, { setSavedFiles } from "stores/chat";
import useUtilStore, { setSelectedFAQPrompt } from "stores/util";
import { PromptTypes } from "types";
import {
  createTempChat,
  formatEntityConfiguration,
  getFileExtension,
  getRecentChatHistory,
} from "utils";
import { v4 as uuidv4 } from "uuid";
import InputActions from "./InputActions";
import MessageInput from "./MessageInput";
import "./styles.scss";

const rollbar = new Rollbar({
  accessToken: import.meta.env.VITE_ROLLBAR_ACCESS_TOKEN,
  environment: import.meta.env.VITE_ROLLBAR_ENVIRONMENT,
  captureUncaught: true,
  captureUnhandledRejections: true,
});

const ChatInput = ({ messageHistory }: any) => {
  const entityConfiguration = useUtilStore(
    (state) => state.entityConfiguration
  );
  const savedFiles = useChatStore((state) => state.savedFiles);

  const selectedFAQPrompt: any = useUtilStore(
    (state) => state.selectedFAQPrompt
  );

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { id: chat_id } = useParams();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(false);
  const [aiResult, setAIResult] = useState<any>({});
  const [toolsConfig, setToolsConfig] = useState({});

  const { mutateAsync: sendMessage } = useSendMessageMutation();

  const transcriptionConfig = useTranscription({ textareaRef });
  const { stopAndClearRecording } = transcriptionConfig || {};

  const {
    handleFileChange,
    fileRef,
    uploadProgress,
    handleRemoveFile,
    selectedFile,
    selectedDocID,
    uploadDocChatId,
  } = useFileUpload({
    setLoading,
  });

  const { isDragging, onDragOver, onDragLeave, onDrop } =
    useDragAndDrop(handleFileChange);
  const { onPaste } = usePasteFile(handleFileChange);

  const addFilesToMetaData = () => {
    if (!selectedFAQPrompt) return [];

    const { id, type, title, context, sub_context } = selectedFAQPrompt || {};

    const faqObject = {
      id,
      type,
      title,
      context,
      sub_context,
    };
    const files = [faqObject];
    return files;
  };

  const getPayload = (inputValue: string, newChatId: string) => {
    const privateAIConfig = formatEntityConfiguration(entityConfiguration);
    const payloadObj: any = {
      ...privateAIConfig,
      text: inputValue,
      chat_id: chat_id ?? newChatId,
      message_type: selectedFile ? "document" : "text",
      chat_history: getRecentChatHistory(messageHistory),
    };

    if (selectedFile) {
      payloadObj.metadata = {
        name: selectedFile?.name,
        type: selectedFile?.type,
        size: selectedFile?.size,
      };

      if (selectedFile?.type.startsWith("image/")) {
        const chatId = chat_id ?? newChatId;
        payloadObj.metadata.path = `chatredact/${chatId}/${selectedDocID}.${getFileExtension(
          selectedFile?.name
        )}`;
        payloadObj.message_type = "media";
      }
    }

    if (selectedFAQPrompt?.type === PromptTypes.FAQ_PROMPT) {
      const filesArray = addFilesToMetaData();
      payloadObj.metadata = {
        ...payloadObj.metadata,
        files: filesArray,
      };
      payloadObj.files = filesArray;
      setSavedFiles(filesArray);
    }

    if (savedFiles?.length) {
      payloadObj.files = savedFiles;
    }

    return payloadObj;
  };

  const handleSendMsgApi = async (payload: any) => {
    setLoading(true);
    if (toolsConfig && Object.keys(toolsConfig).length) {
      payload.tools = toolsConfig;
    }
    const result: any = await sendMessage(payload);
    if (result?.success) {
      setAIResult({ ...result?.data });
    } else {
      setTimeout(() => {
        setMessageHistory((prev: any) => {
          prev.pop();
          return prev;
        });
      }, 1500);
    }
  };

  const onSendMessage = async () => {
    if (loading) return;
    setIsDocDeleted(false);
    stopAndClearRecording();

    if (!textareaRef.current || !textareaRef.current.value) return;

    if (selectedDocID) {
      if (!textareaRef.current || !textareaRef.current.value) return;
    }

    const inputValue = textareaRef.current.value;
    const newChatId = uploadDocChatId ?? uuidv4();

    const payload = getPayload(inputValue, newChatId);
    try {
      if (!chat_id) {
        createTempChat({ inputValue, newChatId, navigate });
      }

      setMessageHistory((prev: any) => [
        ...prev,
        {
          text: payload?.text,
          reid_text: "",
          message_type: payload?.message_type,
          metadata: payload?.metadata,
        },
      ]);
      textareaRef.current.value = "";
      textareaRef.current.style.height = "54px";

      if (selectedFAQPrompt?.type === PromptTypes.FAQ_PROMPT) {
        setSelectedFAQPrompt(null);
      }
      if (selectedDocID) {
        handleRemoveFile();
        setMessageHistory((prev: any) =>
          prev.map((item: any, idx: any) => {
            if (idx === prev?.length - 1) {
              return {
                text: payload?.text,
                reid_text: "",
                message_type: payload?.message_type,
                metadata: {
                  ...payload?.metadata,
                  preview_url: URL.createObjectURL(selectedFile),
                },
              };
            }
            return item;
          })
        );
        await handleSendMsgApi({
          ...payload,
          metadata: {
            ...payload?.metadata,
            doc_id: selectedDocID,
          },
        });
      } else {
        await handleSendMsgApi(payload);
      }
    } catch (err: any) {
      rollbar.error("Error fetching data!", err, {
        chat_id: payload?.chat_id,
        enable_privacy: payload?.enable_privacy,
        text: payload?.text,
      });
      setTimeout(() => {
        setMessageHistory((prev: any) => {
          prev.pop();
          return prev;
        });
        if (!chat_id) {
          navigate(ROUTE_PATH.HOME);
          setTempNewChat(null);
        }
      }, 1500);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (aiResult?.reid_text) {
      setMessageHistory((prev: any) =>
        prev.map((item: any, idx: any) => {
          if (
            idx === messageHistory?.length - 1 &&
            aiResult?.chat_id === chat_id
          ) {
            return aiResult;
          }
          return item;
        })
      );
      setAIResult({});
    }
  }, [aiResult?.reid_text]);

  const isResponseProcessing = useMemo(() => {
    const lastMessage = messageHistory?.[messageHistory?.length - 1];
    if (lastMessage && Object.keys(lastMessage).length) {
      return !!(!lastMessage?.reid_text && chat_id);
    }
    return false;
  }, [messageHistory, messageHistory?.length]);

  return (
    <Form
      className={`position-relative form ${isDragging ? "dragging" : ""}`}
      id="drop_zone"
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
    >
      <div className={isDragging ? "pe-none" : ""}>
        <InputActions
          selectedFile={selectedFile}
          handleRemoveFile={handleRemoveFile}
          selectedDocID={selectedDocID}
          uploadDocChatId={uploadDocChatId}
          handleFileChange={handleFileChange}
          fileRef={fileRef}
          uploadProgress={uploadProgress}
          textareaRef={textareaRef}
          transcriptionConfig={transcriptionConfig}
          selectedFAQPrompt={selectedFAQPrompt}
          toolsConfig={toolsConfig}
          setToolsConfig={setToolsConfig}
        />
      </div>

      <div className="form-input-wrapper">
        <MessageInput
          onSendMessage={onSendMessage}
          textareaRef={textareaRef}
          isDragging={isDragging}
          isResponseProcessing={isResponseProcessing}
          onPaste={onPaste}
        />

        <Button
          variant="Link"
          type="button"
          className="p-0 m-0 chat-submit-btn position-absolute border-0 d-flex"
          onClick={onSendMessage}
          disabled={loading || isResponseProcessing || isDragging}
        >
          <RiArrowUpCircleFill size={"30px"} color="#0d3149" />
        </Button>
      </div>
    </Form>
  );
};

export default ChatInput;
